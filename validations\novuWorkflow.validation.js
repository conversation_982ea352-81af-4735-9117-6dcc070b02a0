const Joi = require('joi');

// Common workflow step schema
const workflowStepSchema = Joi.object({
  _id: Joi.string().optional(),
  name: Joi.string().required(),
  type: Joi.string().valid(
    'in_app', 'email', 'sms', 'chat', 'push', 'digest', 'trigger', 'delay', 'custom'
  ).required(),
  controlValues: Joi.object().optional()
});

// Workflow preferences schema
const workflowPreferencesSchema = Joi.object({
  user: Joi.object({
    all: Joi.object({
      enabled: Joi.boolean().required(),
      readOnly: Joi.boolean().required()
    }).required(),
    channels: Joi.object().pattern(
      Joi.string(),
      Joi.object({
        enabled: Joi.boolean().required()
      })
    ).optional()
  }).optional(),
  workflow: Joi.object({
    all: Joi.object({
      enabled: Joi.boolean().required(),
      readOnly: Joi.boolean().required()
    }).required(),
    channels: Joi.object().pattern(
      Joi.string(),
      Joi.object({
        enabled: Joi.boolean().required()
      })
    ).optional()
  }).optional()
});

// List workflows validation
const listWorkflows = {
  query: Joi.object().keys({
    limit: Joi.number().integer().min(1).max(1000).optional(),
    offset: Joi.number().integer().min(0).optional(),
    orderDirection: Joi.string().valid('ASC', 'DESC').optional(),
    orderBy: Joi.string().valid('createdAt', 'updatedAt', 'name', 'lastTriggeredAt').optional(),
    query: Joi.string().optional(),
    tags: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).optional(),
    status: Joi.alternatives().try(
      Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR'),
      Joi.array().items(Joi.string().valid('ACTIVE', 'INACTIVE', 'ERROR'))
    ).optional()
  })
};

// Get workflow validation
const getWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  })
};

// Create workflow validation
const createWorkflow = {
  body: Joi.object().keys({
    name: Joi.string().required(),
    description: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    active: Joi.boolean().optional().default(false),
    validatePayload: Joi.boolean().optional(),
    payloadSchema: Joi.object().optional(),
    isTranslationEnabled: Joi.boolean().optional().default(false),
    workflowId: Joi.string().required(),
    steps: Joi.array().items(workflowStepSchema).required(),
    __source: Joi.string().valid(
      'template_store', 'editor', 'notification_directory', 'onboarding_digest_demo',
      'onboarding_in_app', 'empty_state', 'dropdown', 'onboarding_get_started',
      'bridge', 'dashboard'
    ).optional().default('editor'),
    preferences: workflowPreferencesSchema.optional()
  })
};

// Update workflow validation
const updateWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  }),
  body: Joi.object().keys({
    name: Joi.string().optional(),
    description: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    active: Joi.boolean().optional(),
    validatePayload: Joi.boolean().optional(),
    payloadSchema: Joi.object().optional(),
    isTranslationEnabled: Joi.boolean().optional(),
    steps: Joi.array().items(workflowStepSchema).optional(),
    preferences: workflowPreferencesSchema.optional()
  })
};

// Delete workflow validation
const deleteWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  })
};

// Sync workflow validation
const syncWorkflow = {
  params: Joi.object().keys({
    workflowId: Joi.string().required()
  }),
  body: Joi.object().optional()
};

// Export workflows validation
const exportWorkflows = {
  body: Joi.object().keys({
    workflowIds: Joi.array().items(Joi.string()).optional()
  })
};

// Bulk export workflows validation
const bulkExportWorkflows = {
  query: Joi.object().keys({
    workflowIds: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).required()
  })
};

// Import workflows validation
const importWorkflows = {
  body: Joi.object().keys({
    importData: Joi.object({
      exportedAt: Joi.string().isoDate().required(),
      version: Joi.string().required(),
      totalWorkflows: Joi.number().integer().min(0).required(),
      workflows: Joi.array().items(
        Joi.object({
          name: Joi.string().required(),
          description: Joi.string().optional(),
          tags: Joi.array().items(Joi.string()).optional(),
          active: Joi.boolean().optional(),
          validatePayload: Joi.boolean().optional(),
          payloadSchema: Joi.object().optional(),
          isTranslationEnabled: Joi.boolean().optional(),
          workflowId: Joi.string().required(),
          steps: Joi.array().items(workflowStepSchema).required(),
          preferences: workflowPreferencesSchema.optional(),
          originalId: Joi.string().optional(),
          originalWorkflowId: Joi.string().optional()
        })
      ).required()
    }).required(),
    options: Joi.object({
      overwriteExisting: Joi.boolean().optional().default(false),
      skipExisting: Joi.boolean().optional().default(true),
      updateWorkflowIds: Joi.boolean().optional().default(true)
    }).optional()
  })
};

module.exports = {
  listWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  syncWorkflow,
  exportWorkflows,
  bulkExportWorkflows,
  importWorkflows
};
