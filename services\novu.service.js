const config = require('../config/config');
const logger = require('../config/logger');

class NovuService {
  constructor() {
    this.apiUrl = 'https://api.novu.co/v1/events/trigger';
    this.subscribersUrl = 'https://api.novu.co/v1/subscribers';
    this.workflowsUrl = 'https://api.novu.co/v2/workflows';
    this.apiKey = config.novu.apiKey;
    this.isEnabled = config.novu.enabled;

    if (this.isEnabled) {
      this.init();
    }
  }

  init() {
    try {
      if (!this.apiKey) {
        throw new Error('Novu API key is required when Novu is enabled');
      }

      const queueArg = process.argv.find((arg) => arg.startsWith("--queue="));
      const isNotificationQueue = queueArg && queueArg.includes('notification');
      
      if (isNotificationQueue) {
        logger.info('Novu service initialized successfully with REST API');
      }
      // logger.info('Novu service initialized successfully with REST API');
    } catch (error) {
      logger.error('Failed to initialize Novu service:', error.message);
      this.isEnabled = false;
    }
  }

  isNovuEnabled() {
    return this.isEnabled && this.apiKey;
  }

  async createOrUpdateSubscriber(subscriberId, subscriberData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      // Prepare subscriber data with subscriberId
      const payload = {
        subscriberId: subscriberId,
        ...subscriberData
      };

      // Use POST to /subscribers for creating/updating (Novu handles both)
      const response = await fetch(this.subscribersUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Subscriber ${subscriberId} created/updated successfully`);
      return result;
    } catch (error) {
      logger.error(`Error creating/updating subscriber ${subscriberId}:`, error.message);
      throw error;
    }
  }

  // async triggerNotification(workflowId, subscriberId, payload, options = {}) {
  //   if (!this.isNovuEnabled()) {
  //     throw new Error('Novu service is not enabled or initialized');
  //   }

  //   try {
  //     const result = await this.novu.trigger(workflowId, {
  //       to: {
  //         subscriberId: subscriberId,
  //       },
  //       payload: payload,
  //     }, options.transactionId);

  //     logger.info(`Notification triggered successfully for workflow ${workflowId} to subscriber ${subscriberId}`);
  //     return result;
  //   } catch (error) {
  //     logger.error(`Error triggering notification for workflow ${workflowId}:`, error.message);
  //     throw error;
  //   }
  // }

  async sendNotification(notificationData) {
    if (!this.isNovuEnabled()) {
      logger.warn('Novu is disabled, skipping notification');
      return null;
    }

    try {
      const {
        workflowId,
        subscriberId,
        subscriberData,
        payload,
        transactionId
      } = notificationData;

      // Create or update subscriber if subscriber data is provided
      if (subscriberData) {
        await this.createOrUpdateSubscriber(subscriberId, subscriberData);
      }

      // Prepare the trigger request payload
      const requestPayload = {
        name: workflowId,
        to: {
          subscriberId: subscriberId,
        },
        payload: payload,
      };

      if (transactionId) {
        requestPayload.transactionId = transactionId;
      }

      // Trigger the notification via REST API
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestPayload)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Notification triggered successfully for workflow ${workflowId} to subscriber ${subscriberId}`);
      return result;
    } catch (error) {
      logger.error('Error sending Novu notification:', error.message);
      throw error;
    }
  }

  async sendDigestNotification(notificationData){
    if (!this.isNovuEnabled()) {
      logger.warn('Novu is disabled, skipping notification');
      return null;
    }
    if(!config.novu.digestWorkflowId){
      logger.warn('Novu digest workflow ID is not configured, skipping notification');
      return null;
    }

    const {transactionId, subscriberId, subscriberData, payload } = notificationData;

    return this.sendNotification({
      workflowId: config?.novu?.digestWorkflowId,
      subscriberId,
      subscriberData,
      payload,
      transactionId
    });
  }

  // Workflow Management Methods

  /**
   * List all workflows with optional filtering and pagination
   * @param {Object} options - Query options for filtering workflows
   * @returns {Promise<Object>} List of workflows with metadata
   */
  async listWorkflows(options = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const queryParams = new URLSearchParams();

      if (options.limit) queryParams.append('limit', options.limit);
      if (options.offset) queryParams.append('offset', options.offset);
      if (options.orderDirection) queryParams.append('orderDirection', options.orderDirection);
      if (options.orderBy) queryParams.append('orderBy', options.orderBy);
      if (options.query) queryParams.append('query', options.query);
      if (options.tags && Array.isArray(options.tags)) {
        options.tags.forEach(tag => queryParams.append('tags', tag));
      }
      if (options.status && Array.isArray(options.status)) {
        options.status.forEach(status => queryParams.append('status', status));
      }

      const url = `${this.workflowsUrl}?${queryParams.toString()}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Retrieved ${result.workflows?.length || 0} workflows from Novu`);
      return result;
    } catch (error) {
      logger.error('Error listing Novu workflows:', error.message);
      throw error;
    }
  }

  /**
   * Retrieve a specific workflow by ID
   * @param {string} workflowId - The workflow identifier
   * @returns {Promise<Object>} Workflow details
   */
  async getWorkflow(workflowId) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}`, {
        method: 'GET',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Retrieved workflow ${workflowId} from Novu`);
      return result;
    } catch (error) {
      logger.error(`Error retrieving workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  /**
   * Create a new workflow
   * @param {Object} workflowData - Workflow configuration data
   * @returns {Promise<Object>} Created workflow details
   */
  async createWorkflow(workflowData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(this.workflowsUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Created workflow ${result.workflowId} in Novu`);
      return result;
    } catch (error) {
      logger.error('Error creating Novu workflow:', error.message);
      throw error;
    }
  }

  /**
   * Update an existing workflow
   * @param {string} workflowId - The workflow identifier
   * @param {Object} workflowData - Updated workflow configuration data
   * @returns {Promise<Object>} Updated workflow details
   */
  async updateWorkflow(workflowId, workflowData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Updated workflow ${workflowId} in Novu`);
      return result;
    } catch (error) {
      logger.error(`Error updating workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  /**
   * Delete a workflow
   * @param {string} workflowId - The workflow identifier
   * @returns {Promise<boolean>} Success status
   */
  async deleteWorkflow(workflowId) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Deleted workflow ${workflowId} from Novu`);
      return true;
    } catch (error) {
      logger.error(`Error deleting workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  /**
   * Sync a workflow (useful for external workflows)
   * @param {string} workflowId - The workflow identifier
   * @param {Object} syncData - Sync configuration data
   * @returns {Promise<Object>} Sync result
   */
  async syncWorkflow(workflowId, syncData = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      const response = await fetch(`${this.workflowsUrl}/${workflowId}/sync`, {
        method: 'PUT',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(syncData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Synced workflow ${workflowId} in Novu`);
      return result;
    } catch (error) {
      logger.error(`Error syncing workflow ${workflowId}:`, error.message);
      throw error;
    }
  }

  // Import/Export Utility Methods

  /**
   * Export workflows to a JSON format for backup or migration
   * @param {Array<string>} workflowIds - Optional array of specific workflow IDs to export
   * @returns {Promise<Object>} Exported workflows data
   */
  async exportWorkflows(workflowIds = null) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      let workflows = [];

      if (workflowIds && Array.isArray(workflowIds)) {
        // Export specific workflows
        for (const workflowId of workflowIds) {
          const workflow = await this.getWorkflow(workflowId);
          workflows.push(workflow);
        }
      } else {
        // Export all workflows
        const result = await this.listWorkflows({ limit: 1000 }); // Adjust limit as needed
        workflows = result.workflows || [];

        // Get full details for each workflow
        const detailedWorkflows = [];
        for (const workflow of workflows) {
          const fullWorkflow = await this.getWorkflow(workflow.workflowId);
          detailedWorkflows.push(fullWorkflow);
        }
        workflows = detailedWorkflows;
      }

      const exportData = {
        exportedAt: new Date().toISOString(),
        version: '1.0',
        totalWorkflows: workflows.length,
        workflows: workflows.map(workflow => ({
          // Remove system-generated fields that shouldn't be imported
          name: workflow.name,
          description: workflow.description,
          tags: workflow.tags,
          active: workflow.active,
          validatePayload: workflow.validatePayload,
          payloadSchema: workflow.payloadSchema,
          isTranslationEnabled: workflow.isTranslationEnabled,
          workflowId: workflow.workflowId,
          steps: workflow.steps,
          preferences: workflow.preferences,
          // Keep original IDs for reference
          originalId: workflow._id,
          originalWorkflowId: workflow.workflowId
        }))
      };

      logger.info(`Exported ${workflows.length} workflows from Novu`);
      return exportData;
    } catch (error) {
      logger.error('Error exporting Novu workflows:', error.message);
      throw error;
    }
  }

  /**
   * Import workflows from exported JSON data
   * @param {Object} importData - Exported workflows data
   * @param {Object} options - Import options
   * @returns {Promise<Object>} Import results
   */
  async importWorkflows(importData, options = {}) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    const {
      overwriteExisting = false,
      skipExisting = true,
      updateWorkflowIds = true
    } = options;

    try {
      const results = {
        imported: [],
        skipped: [],
        errors: [],
        total: importData.workflows?.length || 0
      };

      if (!importData.workflows || !Array.isArray(importData.workflows)) {
        throw new Error('Invalid import data: workflows array is required');
      }

      for (const workflowData of importData.workflows) {
        try {
          let finalWorkflowId = workflowData.workflowId;

          // Generate new workflow ID if requested
          if (updateWorkflowIds) {
            finalWorkflowId = `${workflowData.workflowId}_imported_${Date.now()}`;
          }

          // Check if workflow already exists
          let existingWorkflow = null;
          try {
            existingWorkflow = await this.getWorkflow(finalWorkflowId);
          } catch (error) {
            // Workflow doesn't exist, which is fine for import
          }

          if (existingWorkflow) {
            if (skipExisting) {
              results.skipped.push({
                workflowId: finalWorkflowId,
                reason: 'Workflow already exists and skipExisting is true'
              });
              continue;
            } else if (overwriteExisting) {
              // Update existing workflow
              const updatedWorkflow = await this.updateWorkflow(finalWorkflowId, {
                ...workflowData,
                workflowId: finalWorkflowId
              });
              results.imported.push({
                workflowId: finalWorkflowId,
                action: 'updated',
                workflow: updatedWorkflow
              });
            } else {
              results.errors.push({
                workflowId: finalWorkflowId,
                error: 'Workflow already exists and overwriteExisting is false'
              });
            }
          } else {
            // Create new workflow
            const newWorkflow = await this.createWorkflow({
              ...workflowData,
              workflowId: finalWorkflowId
            });
            results.imported.push({
              workflowId: finalWorkflowId,
              action: 'created',
              workflow: newWorkflow
            });
          }
        } catch (error) {
          results.errors.push({
            workflowId: workflowData.workflowId,
            error: error.message
          });
        }
      }

      logger.info(`Import completed: ${results.imported.length} imported, ${results.skipped.length} skipped, ${results.errors.length} errors`);
      return results;
    } catch (error) {
      logger.error('Error importing Novu workflows:', error.message);
      throw error;
    }
  }
}

// Create singleton instance
const novuService = new NovuService();

module.exports = novuService;
