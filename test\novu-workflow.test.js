/**
 * Test script for Novu Workflow Management System
 * This script tests the core functionality without making actual API calls
 */

const novuService = require('../services/novu.service');
const NovuWorkflowController = require('../controllers/novuWorkflow.controller');

// Mock configuration for testing
const mockConfig = {
  novu: {
    enabled: true,
    apiKey: 'test-api-key'
  }
};

// Mock request and response objects
const createMockReq = (body = {}, params = {}, query = {}) => ({
  body,
  params,
  query
});

const createMockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.setHeader = jest.fn().mockReturnValue(res);
  return res;
};

// Test data
const testWorkflowData = {
  name: "Test Welcome Email",
  description: "A test welcome email workflow",
  workflowId: "test-welcome-email",
  tags: ["test", "welcome"],
  active: true,
  steps: [
    {
      name: "Welcome Email Step",
      type: "email",
      controlValues: {
        subject: "Welcome {{subscriber.firstName}}!",
        body: "Hello {{subscriber.firstName}}, welcome to our test platform!"
      }
    }
  ],
  preferences: {
    user: {
      all: { enabled: true, readOnly: false },
      channels: {
        email: { enabled: true }
      }
    }
  }
};

const testExportData = {
  exportedAt: new Date().toISOString(),
  version: "1.0",
  totalWorkflows: 1,
  workflows: [testWorkflowData]
};

// Test functions
async function testServiceInitialization() {
  console.log('🧪 Testing Novu Service Initialization...');
  
  try {
    // Test if service is properly initialized
    const isEnabled = novuService.isNovuEnabled();
    console.log(`✅ Service enabled status: ${isEnabled}`);
    
    // Test URL construction
    console.log(`✅ Workflows URL: ${novuService.workflowsUrl}`);
    console.log(`✅ API Key configured: ${!!novuService.apiKey}`);
    
    return true;
  } catch (error) {
    console.error('❌ Service initialization failed:', error.message);
    return false;
  }
}

async function testValidationSchemas() {
  console.log('\n🧪 Testing Validation Schemas...');
  
  const Joi = require('joi');
  const NovuWorkflowValidation = require('../validations/novuWorkflow.validation');
  
  try {
    // Test create workflow validation
    const createResult = await Joi.compile(NovuWorkflowValidation.createWorkflow.body)
      .validateAsync(testWorkflowData);
    console.log('✅ Create workflow validation passed');
    
    // Test list workflows validation
    const listQuery = { limit: 10, offset: 0, orderBy: 'createdAt' };
    const listResult = await Joi.compile(NovuWorkflowValidation.listWorkflows.query)
      .validateAsync(listQuery);
    console.log('✅ List workflows validation passed');
    
    // Test import validation
    const importData = {
      importData: testExportData,
      options: {
        overwriteExisting: false,
        skipExisting: true,
        updateWorkflowIds: true
      }
    };
    const importResult = await Joi.compile(NovuWorkflowValidation.importWorkflows.body)
      .validateAsync(importData);
    console.log('✅ Import workflows validation passed');
    
    return true;
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    return false;
  }
}

async function testControllerMethods() {
  console.log('\n🧪 Testing Controller Methods...');
  
  try {
    // Test if controller methods exist and are functions
    const methods = [
      'listWorkflows',
      'getWorkflow', 
      'createWorkflow',
      'updateWorkflow',
      'deleteWorkflow',
      'syncWorkflow',
      'exportWorkflows',
      'importWorkflows',
      'bulkExportWorkflows',
      'getExportTemplate'
    ];
    
    for (const method of methods) {
      if (typeof NovuWorkflowController[method] === 'function') {
        console.log(`✅ Controller method exists: ${method}`);
      } else {
        throw new Error(`Controller method missing: ${method}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Controller test failed:', error.message);
    return false;
  }
}

async function testExportDataStructure() {
  console.log('\n🧪 Testing Export Data Structure...');
  
  try {
    // Test export data structure
    const exportData = {
      exportedAt: new Date().toISOString(),
      version: "1.0",
      totalWorkflows: 1,
      workflows: [
        {
          name: "Test Workflow",
          workflowId: "test-workflow",
          description: "Test description",
          tags: ["test"],
          active: true,
          steps: [
            {
              name: "Test Step",
              type: "email",
              controlValues: {
                subject: "Test Subject",
                body: "Test Body"
              }
            }
          ]
        }
      ]
    };
    
    // Validate required fields
    if (!exportData.exportedAt) throw new Error('Missing exportedAt');
    if (!exportData.version) throw new Error('Missing version');
    if (typeof exportData.totalWorkflows !== 'number') throw new Error('Invalid totalWorkflows');
    if (!Array.isArray(exportData.workflows)) throw new Error('Invalid workflows array');
    
    console.log('✅ Export data structure is valid');
    console.log(`✅ Export contains ${exportData.totalWorkflows} workflow(s)`);
    
    return true;
  } catch (error) {
    console.error('❌ Export data structure test failed:', error.message);
    return false;
  }
}

async function testImportOptions() {
  console.log('\n🧪 Testing Import Options...');
  
  try {
    const validOptions = [
      { overwriteExisting: true, skipExisting: false, updateWorkflowIds: true },
      { overwriteExisting: false, skipExisting: true, updateWorkflowIds: false },
      { overwriteExisting: true, skipExisting: true, updateWorkflowIds: true }
    ];
    
    for (const options of validOptions) {
      // Test option combinations
      if (options.overwriteExisting && options.skipExisting) {
        console.log('⚠️  Warning: Both overwriteExisting and skipExisting are true');
      }
      console.log(`✅ Valid option combination: ${JSON.stringify(options)}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Import options test failed:', error.message);
    return false;
  }
}

async function testWorkflowStepTypes() {
  console.log('\n🧪 Testing Workflow Step Types...');
  
  try {
    const validStepTypes = [
      'in_app', 'email', 'sms', 'chat', 'push', 'digest', 'trigger', 'delay', 'custom'
    ];
    
    for (const stepType of validStepTypes) {
      const testStep = {
        name: `Test ${stepType} Step`,
        type: stepType,
        controlValues: {}
      };
      
      console.log(`✅ Valid step type: ${stepType}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Step types test failed:', error.message);
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🧪 Testing Error Handling...');
  
  try {
    // Test various error scenarios
    const errorScenarios = [
      {
        name: 'Missing required fields',
        data: { name: 'Test' }, // Missing workflowId and steps
        shouldFail: true
      },
      {
        name: 'Invalid step type',
        data: {
          name: 'Test',
          workflowId: 'test',
          steps: [{ name: 'Test', type: 'invalid_type' }]
        },
        shouldFail: true
      },
      {
        name: 'Valid workflow data',
        data: testWorkflowData,
        shouldFail: false
      }
    ];
    
    const Joi = require('joi');
    const NovuWorkflowValidation = require('../validations/novuWorkflow.validation');
    
    for (const scenario of errorScenarios) {
      try {
        await Joi.compile(NovuWorkflowValidation.createWorkflow.body)
          .validateAsync(scenario.data);
        
        if (scenario.shouldFail) {
          console.log(`⚠️  Expected failure but passed: ${scenario.name}`);
        } else {
          console.log(`✅ Validation passed as expected: ${scenario.name}`);
        }
      } catch (error) {
        if (scenario.shouldFail) {
          console.log(`✅ Validation failed as expected: ${scenario.name}`);
        } else {
          console.log(`❌ Unexpected validation failure: ${scenario.name} - ${error.message}`);
        }
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error handling test failed:', error.message);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Novu Workflow Management System Tests\n');
  
  const tests = [
    testServiceInitialization,
    testValidationSchemas,
    testControllerMethods,
    testExportDataStructure,
    testImportOptions,
    testWorkflowStepTypes,
    testErrorHandling
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test failed with exception: ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The Novu Workflow Management System is ready for use.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
  }
}

// Export for use in other test files
module.exports = {
  runTests,
  testWorkflowData,
  testExportData,
  createMockReq,
  createMockRes
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}
