# Novu Template Deployment (Import/Export) Guide

This guide explains how to use the Novu template deployment system to import and export notification workflows using REST API.

## Overview

The Novu template deployment system provides comprehensive REST API endpoints for:
- Creating, reading, updating, and deleting workflows
- Exporting workflows to JSON format for backup or migration
- Importing workflows from JSON data
- Bulk operations for multiple workflows
- Template management and synchronization

## API Endpoints

### Workflow Management

#### List Workflows
```http
GET /api/novu-workflows
```

Query Parameters:
- `limit` (integer): Number of workflows to return (1-1000)
- `offset` (integer): Number of workflows to skip
- `orderDirection` (string): ASC or DESC
- `orderBy` (string): createdAt, updatedAt, name, lastTriggeredAt
- `query` (string): Search query to filter workflows
- `tags` (array): Filter by tags
- `status` (array): Filter by status (ACTIVE, INACTIVE, ERROR)

#### Get Specific Workflow
```http
GET /api/novu-workflows/{workflowId}
```

#### Create Workflow
```http
POST /api/novu-workflows
```

Request Body:
```json
{
  "name": "Welcome Email Workflow",
  "description": "Send welcome email to new users",
  "workflowId": "welcome-email-workflow",
  "tags": ["onboarding", "email"],
  "active": true,
  "steps": [
    {
      "name": "Welcome Email",
      "type": "email",
      "controlValues": {
        "subject": "Welcome {{subscriber.firstName}}!",
        "body": "Hello {{subscriber.firstName}}, welcome to our platform!"
      }
    }
  ]
}
```

#### Update Workflow
```http
PUT /api/novu-workflows/{workflowId}
```

#### Delete Workflow
```http
DELETE /api/novu-workflows/{workflowId}
```

#### Sync Workflow
```http
PUT /api/novu-workflows/{workflowId}/sync
```

### Import/Export Operations

#### Export All Workflows
```http
POST /api/novu-workflows/export
```

Request Body (optional):
```json
{
  "workflowIds": ["workflow-1", "workflow-2"]
}
```

#### Bulk Export Specific Workflows
```http
GET /api/novu-workflows/export/bulk?workflowIds=workflow-1,workflow-2
```

#### Import Workflows
```http
POST /api/novu-workflows/import
```

Request Body:
```json
{
  "importData": {
    "exportedAt": "2024-01-01T00:00:00.000Z",
    "version": "1.0",
    "totalWorkflows": 1,
    "workflows": [
      {
        "name": "Example Workflow",
        "workflowId": "example-workflow",
        "steps": [...]
      }
    ]
  },
  "options": {
    "overwriteExisting": false,
    "skipExisting": true,
    "updateWorkflowIds": true
  }
}
```

#### Get Export Template
```http
GET /api/novu-workflows/export/template
```

## Usage Examples

### 1. Export All Workflows

```bash
curl -X POST "https://your-api.com/api/novu-workflows/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. Export Specific Workflows

```bash
curl -X POST "https://your-api.com/api/novu-workflows/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "workflowIds": ["welcome-email", "password-reset"]
  }'
```

### 3. Import Workflows

```bash
curl -X POST "https://your-api.com/api/novu-workflows/import" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "importData": {
      "exportedAt": "2024-01-01T00:00:00.000Z",
      "version": "1.0",
      "totalWorkflows": 1,
      "workflows": [...]
    },
    "options": {
      "overwriteExisting": false,
      "skipExisting": true,
      "updateWorkflowIds": true
    }
  }'
```

### 4. Create a New Workflow

```bash
curl -X POST "https://your-api.com/api/novu-workflows" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "User Registration Notification",
    "workflowId": "user-registration",
    "description": "Notify admins when a new user registers",
    "active": true,
    "steps": [
      {
        "name": "Admin Email Notification",
        "type": "email",
        "controlValues": {
          "subject": "New User Registration: {{payload.userName}}",
          "body": "A new user {{payload.userName}} has registered with email {{payload.userEmail}}"
        }
      }
    ]
  }'
```

## Import Options

### overwriteExisting
- `true`: Update existing workflows with the same ID
- `false` (default): Skip or error on existing workflows

### skipExisting
- `true` (default): Skip workflows that already exist
- `false`: Error on existing workflows (unless overwriteExisting is true)

### updateWorkflowIds
- `true` (default): Generate new workflow IDs with timestamp suffix
- `false`: Use original workflow IDs from export

## Export Data Structure

```json
{
  "exportedAt": "2024-01-01T00:00:00.000Z",
  "version": "1.0",
  "totalWorkflows": 2,
  "workflows": [
    {
      "name": "Welcome Email",
      "description": "Send welcome email to new users",
      "tags": ["onboarding"],
      "active": true,
      "workflowId": "welcome-email",
      "steps": [
        {
          "name": "Email Step",
          "type": "email",
          "controlValues": {
            "subject": "Welcome!",
            "body": "Welcome to our platform!"
          }
        }
      ],
      "preferences": {
        "user": {
          "all": { "enabled": true, "readOnly": false },
          "channels": {
            "email": { "enabled": true }
          }
        }
      }
    }
  ]
}
```

## Error Handling

The API returns detailed error information for failed operations:

```json
{
  "status": false,
  "message": "Import completed with errors",
  "data": {
    "imported": [
      {
        "workflowId": "workflow-1",
        "action": "created"
      }
    ],
    "skipped": [
      {
        "workflowId": "workflow-2",
        "reason": "Workflow already exists"
      }
    ],
    "errors": [
      {
        "workflowId": "workflow-3",
        "error": "Invalid step configuration"
      }
    ],
    "total": 3
  }
}
```

## Required Permissions

Ensure your API token has the following permissions:
- `view_novu_workflows` - View workflows
- `create_novu_workflow` - Create workflows
- `edit_novu_workflow` - Update workflows
- `delete_novu_workflow` - Delete workflows
- `sync_novu_workflow` - Sync workflows
- `export_novu_workflows` - Export workflows
- `import_novu_workflows` - Import workflows

**Note**: These permissions have been added to `config/permissions.js`. If you're getting "Unauthorized access of identity" errors, make sure:

1. Your user/role has been assigned these permissions
2. The permissions are properly configured in your authentication system
3. Your JWT token includes these permissions in the claims

## Configuration

Make sure your Novu service is properly configured in your environment:

```javascript
// config/config.js
novu: {
  enabled: process.env.NOVU_ENABLED === 'true',
  apiKey: process.env.NOVU_API_KEY,
  digestWorkflowId: process.env.NOVU_DIGEST_WORKFLOW_ID
}
```

Environment variables:
- `NOVU_ENABLED=true`
- `NOVU_API_KEY=your_novu_api_key`
- `NOVU_DIGEST_WORKFLOW_ID=your_digest_workflow_id` (optional)

## Troubleshooting

### "Unauthorized access of identity" Error

This error occurs when your user doesn't have the required permissions. To fix this:

1. **Check Permission Configuration**
   ```bash
   # Verify permissions are added to config/permissions.js
   grep -A 10 "Novu Workflow" config/permissions.js
   ```

2. **Verify User Permissions**
   - Ensure your user/role has been assigned the Novu workflow permissions
   - Check your JWT token includes these permissions in the claims
   - Contact your system administrator to assign the required permissions

3. **Required Permissions List**
   ```javascript
   [
     "view_novu_workflows",
     "create_novu_workflow",
     "edit_novu_workflow",
     "delete_novu_workflow",
     "sync_novu_workflow",
     "export_novu_workflows",
     "import_novu_workflows"
   ]
   ```

4. **Test Permission Assignment**
   ```bash
   # Test with a simple GET request
   curl -X GET "https://your-api.com/api/novu-workflows" \
     -H "Authorization: Bearer YOUR_TOKEN"
   ```

### "Novu service is not enabled or initialized" Error

This error occurs when Novu is not properly configured:

1. **Check Environment Variables**
   ```bash
   echo $NOVU_ENABLED
   echo $NOVU_API_KEY
   ```

2. **Verify Configuration**
   ```javascript
   // config/config.js should have:
   novu: {
     enabled: process.env.NOVU_ENABLED === 'true',
     apiKey: process.env.NOVU_API_KEY
   }
   ```

3. **Restart the Application**
   After updating environment variables, restart your application.

### API Connection Issues

1. **Check Novu API Key**
   - Verify your API key is valid and has the correct permissions
   - Test the key directly with Novu's API

2. **Network Connectivity**
   ```bash
   # Test connectivity to Novu API
   curl -X GET "https://api.novu.co/v2/workflows" \
     -H "Authorization: ApiKey YOUR_NOVU_API_KEY"
   ```

3. **Check Rate Limits**
   - Novu has rate limits that may cause temporary failures
   - Implement retry logic for production use
