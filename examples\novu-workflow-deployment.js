/**
 * Novu Workflow Deployment Example
 * 
 * This example demonstrates how to use the Novu workflow import/export API
 * for template deployment and migration scenarios.
 */

const fetch = require('node-fetch'); // You may need to install: npm install node-fetch
const fs = require('fs').promises;

class NovuWorkflowDeployment {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
    this.headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Export all workflows to a JSON file
   */
  async exportAllWorkflows(filename = null) {
    try {
      console.log('Exporting all workflows...');
      
      const response = await fetch(`${this.apiBaseUrl}/api/novu-workflows/export`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({})
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const exportData = result.data;

      // Save to file if filename provided
      if (filename) {
        await fs.writeFile(filename, JSON.stringify(exportData, null, 2));
        console.log(`Exported ${exportData.totalWorkflows} workflows to ${filename}`);
      }

      return exportData;
    } catch (error) {
      console.error('Export failed:', error.message);
      throw error;
    }
  }

  /**
   * Export specific workflows by IDs
   */
  async exportSpecificWorkflows(workflowIds, filename = null) {
    try {
      console.log(`Exporting workflows: ${workflowIds.join(', ')}`);
      
      const response = await fetch(`${this.apiBaseUrl}/api/novu-workflows/export`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({ workflowIds })
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const exportData = result.data;

      // Save to file if filename provided
      if (filename) {
        await fs.writeFile(filename, JSON.stringify(exportData, null, 2));
        console.log(`Exported ${exportData.totalWorkflows} workflows to ${filename}`);
      }

      return exportData;
    } catch (error) {
      console.error('Export failed:', error.message);
      throw error;
    }
  }

  /**
   * Import workflows from JSON data or file
   */
  async importWorkflows(importData, options = {}) {
    try {
      // If importData is a string, treat it as a filename
      if (typeof importData === 'string') {
        console.log(`Reading import data from ${importData}`);
        const fileContent = await fs.readFile(importData, 'utf8');
        importData = JSON.parse(fileContent);
      }

      console.log(`Importing ${importData.totalWorkflows} workflows...`);

      const defaultOptions = {
        overwriteExisting: false,
        skipExisting: true,
        updateWorkflowIds: true
      };

      const finalOptions = { ...defaultOptions, ...options };

      const response = await fetch(`${this.apiBaseUrl}/api/novu-workflows/import`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({
          importData,
          options: finalOptions
        })
      });

      if (!response.ok) {
        throw new Error(`Import failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const importResult = result.data;

      console.log('Import completed:');
      console.log(`- Imported: ${importResult.imported.length}`);
      console.log(`- Skipped: ${importResult.skipped.length}`);
      console.log(`- Errors: ${importResult.errors.length}`);

      if (importResult.errors.length > 0) {
        console.log('Errors:');
        importResult.errors.forEach(error => {
          console.log(`  - ${error.workflowId}: ${error.error}`);
        });
      }

      return importResult;
    } catch (error) {
      console.error('Import failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a new workflow
   */
  async createWorkflow(workflowData) {
    try {
      console.log(`Creating workflow: ${workflowData.name}`);
      
      const response = await fetch(`${this.apiBaseUrl}/api/novu-workflows`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(workflowData)
      });

      if (!response.ok) {
        throw new Error(`Create failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`Workflow created: ${result.data.workflowId}`);
      
      return result.data;
    } catch (error) {
      console.error('Create failed:', error.message);
      throw error;
    }
  }

  /**
   * List all workflows
   */
  async listWorkflows(options = {}) {
    try {
      const queryParams = new URLSearchParams();
      Object.keys(options).forEach(key => {
        if (options[key] !== undefined) {
          queryParams.append(key, options[key]);
        }
      });

      const url = `${this.apiBaseUrl}/api/novu-workflows?${queryParams.toString()}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`List failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('List failed:', error.message);
      throw error;
    }
  }

  /**
   * Deployment scenario: Backup current workflows
   */
  async backupWorkflows(backupDir = './backups') {
    try {
      // Create backup directory if it doesn't exist
      await fs.mkdir(backupDir, { recursive: true });

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${backupDir}/novu-workflows-backup-${timestamp}.json`;

      await this.exportAllWorkflows(filename);
      console.log(`Backup completed: ${filename}`);
      
      return filename;
    } catch (error) {
      console.error('Backup failed:', error.message);
      throw error;
    }
  }

  /**
   * Deployment scenario: Deploy workflows from staging to production
   */
  async deployFromStaging(stagingExportFile, options = {}) {
    try {
      console.log('Starting deployment from staging...');
      
      // First, create a backup of current production workflows
      const backupFile = await this.backupWorkflows();
      console.log(`Production backup created: ${backupFile}`);

      // Import workflows from staging
      const deployOptions = {
        overwriteExisting: true,
        skipExisting: false,
        updateWorkflowIds: false, // Keep original IDs for deployment
        ...options
      };

      const result = await this.importWorkflows(stagingExportFile, deployOptions);
      
      console.log('Deployment completed successfully!');
      return result;
    } catch (error) {
      console.error('Deployment failed:', error.message);
      throw error;
    }
  }
}

// Example usage
async function example() {
  const deployment = new NovuWorkflowDeployment(
    'https://your-api.com', // Your API base URL
    'your-auth-token'       // Your authentication token
  );

  try {
    // Example 1: Export all workflows
    await deployment.exportAllWorkflows('./exports/all-workflows.json');

    // Example 2: Export specific workflows
    await deployment.exportSpecificWorkflows(
      ['welcome-email', 'password-reset'],
      './exports/specific-workflows.json'
    );

    // Example 3: Create a new workflow
    const newWorkflow = {
      name: 'Test Notification',
      workflowId: 'test-notification',
      description: 'A test notification workflow',
      active: true,
      steps: [
        {
          name: 'Email Step',
          type: 'email',
          controlValues: {
            subject: 'Test Subject',
            body: 'This is a test email body'
          }
        }
      ]
    };
    await deployment.createWorkflow(newWorkflow);

    // Example 4: Import workflows
    await deployment.importWorkflows('./exports/workflows-to-import.json', {
      overwriteExisting: false,
      skipExisting: true,
      updateWorkflowIds: true
    });

    // Example 5: Backup current workflows
    await deployment.backupWorkflows('./backups');

    // Example 6: Deploy from staging to production
    await deployment.deployFromStaging('./staging-export.json');

  } catch (error) {
    console.error('Example failed:', error.message);
  }
}

// Uncomment to run the example
// example();

module.exports = NovuWorkflowDeployment;
